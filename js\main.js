// ===== MAIN APPLICATION CONTROLLER =====
class RestaurantApp {
    constructor() {
        this.currentPage = 'home';
        this.favorites = JSON.parse(localStorage.getItem('favorites')) || [];
        this.cart = JSON.parse(localStorage.getItem('cart')) || [];
        
        this.init();
    }
    
    // Initialize the application
    async init() {
        this.showLoadingScreen();
        await this.loadInitialData();
        this.setupEventListeners();
        this.setupNavigation();
        this.hideLoadingScreen();
    }
    
    // Show loading screen
    showLoadingScreen() {
        animationController.showLoadingScreen();
    }
    
    // Hide loading screen
    hideLoadingScreen() {
        setTimeout(() => {
            animationController.hideLoadingScreen();
        }, 1500);
    }
    
    // Load initial data
    async loadInitialData() {
        try {
            const categories = await api.getCategories();
            this.renderCategories(categories);
        } catch (error) {
            console.error('Error loading initial data:', error);
            this.showError('حدث خطأ في تحميل البيانات');
        }
    }
    
    // Setup event listeners
    setupEventListeners() {
        // Mobile menu toggle
        const navToggle = document.querySelector('.nav-toggle');
        const navMenu = document.querySelector('.nav-menu');
        
        if (navToggle && navMenu) {
            navToggle.addEventListener('click', () => {
                navMenu.classList.toggle('active');
                this.animateMenuToggle(navToggle, navMenu.classList.contains('active'));
            });
        }
        
        // Smooth scroll for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // Window scroll event for header
        window.addEventListener('scroll', () => {
            this.handleScroll();
        });
        
        // Window resize event
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }
    
    // Setup navigation
    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                // Remove active class from all links
                navLinks.forEach(l => l.classList.remove('active'));
                // Add active class to clicked link
                link.classList.add('active');
            });
        });
    }
    
    // Handle scroll events
    handleScroll() {
        const header = document.querySelector('.header');
        const scrollY = window.scrollY;
        
        if (scrollY > 100) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
        
        // Update active navigation based on scroll position
        this.updateActiveNavigation();
    }
    
    // Handle resize events
    handleResize() {
        // Close mobile menu on resize to desktop
        if (window.innerWidth > 768) {
            const navMenu = document.querySelector('.nav-menu');
            if (navMenu) {
                navMenu.classList.remove('active');
            }
        }
        
        // Refresh animations
        animationController.refresh();
    }
    
    // Update active navigation based on scroll position
    updateActiveNavigation() {
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('.nav-link[href^="#"]');
        
        let currentSection = '';
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop - 100;
            const sectionHeight = section.offsetHeight;
            
            if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
                currentSection = section.getAttribute('id');
            }
        });
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${currentSection}`) {
                link.classList.add('active');
            }
        });
    }
    
    // Animate menu toggle
    animateMenuToggle(toggle, isOpen) {
        const spans = toggle.querySelectorAll('span');
        
        if (isOpen) {
            gsap.to(spans[0], { rotation: 45, y: 6, duration: 0.3 });
            gsap.to(spans[1], { opacity: 0, duration: 0.3 });
            gsap.to(spans[2], { rotation: -45, y: -6, duration: 0.3 });
        } else {
            gsap.to(spans[0], { rotation: 0, y: 0, duration: 0.3 });
            gsap.to(spans[1], { opacity: 1, duration: 0.3 });
            gsap.to(spans[2], { rotation: 0, y: 0, duration: 0.3 });
        }
    }
    
    // Render categories
    renderCategories(categories) {
        const categoriesGrid = $('#categoriesGrid');
        if (!categoriesGrid.length) return;

        categoriesGrid.empty();

        categories.forEach((category, index) => {
            const categoryCard = this.createCategoryCard(category, index);
            categoriesGrid.append(categoryCard);
        });

        // Refresh AOS animations
        AOS.refresh();
    }
    
    // Create category card element
    createCategoryCard(category, index) {
        const delay = index * 100;

        const cardHtml = `
            <div class="col-lg-4 col-md-6 col-sm-12">
                <div class="category-card hover-lift" data-aos="fade-up" data-aos-delay="${delay}" data-category-id="${category.id}">
                    <div class="category-image" style="background-image: url('${category.image}')">
                        <div class="category-overlay"></div>
                    </div>
                    <div class="category-info">
                        <h3 class="category-name">${category.name}</h3>
                        <p class="category-description">${category.description}</p>
                        <div class="category-stats">
                            <span><i class="fas fa-utensils me-1"></i>${category.itemsCount} طبق</span>
                            <span><i class="fas fa-check-circle me-1"></i>متوفر الآن</span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        const $card = $(cardHtml);

        // Add click event using jQuery
        $card.find('.category-card').on('click', () => {
            this.navigateToCategory(category.id);
        });

        return $card;
    }
    
    // Navigate to category page
    navigateToCategory(categoryId) {
        animationController.animatePageTransition(() => {
            window.location.href = `categoryItems.html?category=${categoryId}`;
        });
    }
    
    // Navigate to item page
    navigateToItem(itemId) {
        animationController.animatePageTransition(() => {
            window.location.href = `item.html?item=${itemId}`;
        });
    }
    
    // Add to favorites
    addToFavorites(itemId) {
        if (!this.favorites.includes(itemId)) {
            this.favorites.push(itemId);
            this.saveFavorites();
            this.showNotification('تم إضافة العنصر للمفضلة', 'success');
        }
    }
    
    // Remove from favorites
    removeFromFavorites(itemId) {
        this.favorites = this.favorites.filter(id => id !== itemId);
        this.saveFavorites();
        this.showNotification('تم إزالة العنصر من المفضلة', 'info');
    }
    
    // Toggle favorite
    toggleFavorite(itemId) {
        if (this.favorites.includes(itemId)) {
            this.removeFromFavorites(itemId);
            return false;
        } else {
            this.addToFavorites(itemId);
            return true;
        }
    }
    
    // Save favorites to localStorage
    saveFavorites() {
        localStorage.setItem('favorites', JSON.stringify(this.favorites));
    }
    
    // Add to cart
    addToCart(itemId, quantity = 1) {
        const existingItem = this.cart.find(item => item.id === itemId);
        
        if (existingItem) {
            existingItem.quantity += quantity;
        } else {
            this.cart.push({ id: itemId, quantity });
        }
        
        this.saveCart();
        this.updateCartUI();
        this.showNotification('تم إضافة العنصر للسلة', 'success');
    }
    
    // Save cart to localStorage
    saveCart() {
        localStorage.setItem('cart', JSON.stringify(this.cart));
    }
    
    // Update cart UI
    updateCartUI() {
        const cartCount = this.cart.reduce((total, item) => total + item.quantity, 0);
        const cartBadge = document.querySelector('.cart-badge');
        
        if (cartBadge) {
            cartBadge.textContent = cartCount;
            cartBadge.style.display = cartCount > 0 ? 'block' : 'none';
        }
    }
    
    // Show notification
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        notification.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Animate out and remove
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
    
    // Show error message
    showError(message) {
        this.showNotification(message, 'error');
    }
    
}

// Initialize the application when DOM is loaded
$(document).ready(() => {
    window.restaurantApp = new RestaurantApp();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RestaurantApp;
}
