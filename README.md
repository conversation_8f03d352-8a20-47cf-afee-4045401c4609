# منيو المطعم الإلكتروني

## وصف المشروع

منيو مطعم إلكتروني احترافي مصمم بتقنيات الويب الحديثة مع واجهة مستخدم جذابة وتفاعلية. يتضمن المشروع ثلاث صفحات رئيسية مع انيميشن احترافي وتصميم متجاوب.

## المميزات

### 🎨 التصميم
- تصميم احترافي وعصري
- واجهة مستخدم باللغة العربية
- تصميم متجاوب يعمل على جميع الأجهزة
- ألوان متناسقة وجذابة

### ⚡ الانيميشن والتفاعل
- انيميشن متقدم باستخدام GSAP و AOS
- تأثيرات hover احترافية
- انتقالات سلسة بين الصفحات
- شاشة تحميل متحركة

### 🔧 الوظائف
- عرض الأصناف في الصفحة الرئيسية
- صفحة منتجات الصنف مع فلترة وبحث
- صفحة تفاصيل المنتج مع معرض صور
- نظام المفضلة والسلة
- تقييمات العملاء
- منتجات مشابهة

### 📱 التقنيات المستخدمة
- **HTML5**: هيكل الصفحات
- **CSS3**: التصميم والتنسيق
- **JavaScript ES6+**: البرمجة والتفاعل
- **GSAP**: انيميشن متقدم
- **AOS**: انيميشن عند التمرير
- **Swiper.js**: معرض الصور
- **Font Awesome**: الأيقونات
- **Google Fonts**: خط Cairo

## هيكل المشروع

```
men/
├── index.html              # الصفحة الرئيسية
├── categoryItems.html      # صفحة منتجات الصنف
├── item.html              # صفحة تفاصيل المنتج
├── css/
│   ├── style.css          # التصميم الرئيسي
│   └── animations.css     # تصميم الانيميشن
├── js/
│   ├── main.js           # الكود الرئيسي للصفحة الرئيسية
│   ├── categoryItems.js  # كود صفحة منتجات الصنف
│   ├── item.js           # كود صفحة تفاصيل المنتج
│   ├── api.js            # API وإدارة البيانات
│   └── animations.js     # تحكم الانيميشن
├── images/               # مجلد الصور
└── README.md            # ملف التوضيح
```

## الصفحات

### 1. الصفحة الرئيسية (index.html)
- عرض جميع أصناف الطعام
- قسم البطل مع انيميشن جذاب
- شريط التنقل الثابت
- تذييل معلوماتي

### 2. صفحة منتجات الصنف (categoryItems.html)
- عرض جميع منتجات الصنف المحدد
- نظام بحث وفلترة
- ترتيب حسب السعر والشعبية
- إضافة للمفضلة

### 3. صفحة تفاصيل المنتج (item.html)
- معرض صور تفاعلي
- تفاصيل المنتج والمكونات
- القيم الغذائية
- تقييمات العملاء
- منتجات مشابهة
- إضافة للسلة

## كيفية الاستخدام

### 1. تشغيل المشروع
```bash
# افتح الملف في المتصفح مباشرة
open index.html

# أو استخدم خادم محلي
python -m http.server 8000
# ثم افتح http://localhost:8000
```

### 2. التنقل
- ابدأ من الصفحة الرئيسية
- اضغط على أي صنف للانتقال لصفحة المنتجات
- اضغط على أي منتج لعرض التفاصيل

### 3. الميزات التفاعلية
- **البحث**: ابحث عن منتج معين
- **الفلترة**: فلتر حسب النوع (حار، نباتي، شعبي)
- **الترتيب**: رتب حسب الاسم أو السعر
- **المفضلة**: أضف المنتجات للمفضلة
- **السلة**: أضف المنتجات للسلة

## البيانات

المشروع يستخدم API محاكي مع بيانات تجريبية تشمل:
- 6 أصناف رئيسية
- 9 منتجات متنوعة
- تقييمات وتعليقات العملاء
- صور من Unsplash للعرض التوضيحي

## التخصيص

### تغيير البيانات
عدل ملف `js/api.js` لإضافة أو تعديل:
- الأصناف والمنتجات
- الأسعار والأوصاف
- الصور والتقييمات

### تغيير التصميم
عدل ملف `css/style.css` لتخصيص:
- الألوان والخطوط
- التخطيط والأبعاد
- التأثيرات البصرية

### تغيير الانيميشن
عدل ملف `js/animations.js` لتخصيص:
- مدة الانيميشن
- نوع التأثيرات
- توقيت الحركات

## المتطلبات

- متصفح حديث يدعم ES6+
- اتصال بالإنترنت لتحميل المكتبات الخارجية
- خادم ويب (اختياري للتطوير)

## الدعم والتطوير

المشروع قابل للتوسع ويمكن إضافة:
- نظام دفع إلكتروني
- لوحة تحكم إدارية
- قاعدة بيانات حقيقية
- تطبيق جوال
- نظام طلبات

## الترخيص

هذا المشروع مفتوح المصدر ويمكن استخدامه وتعديله بحرية.

---

تم تطوير هذا المشروع بواسطة Augment Agent 🤖
