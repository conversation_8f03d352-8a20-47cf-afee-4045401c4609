/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fafafa;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* ===== LOADING SCREEN ===== */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

#loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loader {
    text-align: center;
    color: white;
}

.chef-hat {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: bounce 2s infinite;
}

.loading-text {
    font-size: 1.2rem;
    font-weight: 600;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-30px);
    }
    60% {
        transform: translateY(-15px);
    }
}

/* ===== HEADER & NAVIGATION ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
    text-decoration: none;
}

.nav-logo i {
    margin-left: 0.5rem;
    font-size: 2rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: #667eea;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #667eea;
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* ===== PAGE HEADER ===== */
.page-header {
    padding: 120px 0 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* ===== SECTIONS ===== */
.categories-section,
.items-section,
.related-items,
.reviews-section {
    padding: 5rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* ===== CATEGORIES GRID ===== */
.categories-section {
    padding: 4rem 0;
    background: #f8f9fa;
}

.category-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    margin-bottom: 2rem;
    height: 100%;
}

.category-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.category-image {
    height: 200px;
    background-size: cover;
    background-position: center;
    position: relative;
}

.category-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.category-card:hover .category-overlay {
    opacity: 1;
}

.category-info {
    padding: 1.5rem;
}

.category-name {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.category-description {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.category-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: #888;
}

/* ===== BREADCRUMB ===== */
.breadcrumb {
    padding: 2rem 0 1rem;
    margin-top: 80px;
    background: #f8f9fa;
}

.breadcrumb-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.breadcrumb-link {
    color: #667eea;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.breadcrumb-link:hover {
    text-decoration: underline;
}

.breadcrumb-separator {
    color: #999;
}

.breadcrumb-current {
    color: #333;
    font-weight: 500;
}

/* ===== FOOTER ===== */
.footer {
    background: #2c3e50;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #ecf0f1;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #667eea;
}

.contact-info p {
    margin-bottom: 0.5rem;
    color: #bdc3c7;
}

.contact-info i {
    margin-left: 0.5rem;
    color: #667eea;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    color: #95a5a6;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 991px) {
    .nav-menu {
        position: fixed;
        top: 80px;
        right: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: white;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: right 0.3s ease;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        z-index: 999;
    }

    .nav-menu.active {
        right: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .page-title {
        font-size: 2.5rem;
    }

    .page-subtitle {
        font-size: 1.1rem;
    }
}

@media (max-width: 768px) {
    .page-header {
        padding: 100px 0 40px;
    }

    .page-title {
        font-size: 2rem;
    }

    .category-card {
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 576px) {
    .page-title {
        font-size: 1.8rem;
    }

    .page-subtitle {
        font-size: 1rem;
    }

    .category-info {
        padding: 1rem;
    }

    .category-name {
        font-size: 1.1rem;
    }
}

/* ===== CATEGORY HEADER ===== */
.category-header {
    padding: 120px 0 2rem;
    background: white;
    margin-bottom: 2rem;
}

.category-image {
    max-width: 200px;
    margin: 0 auto;
}

.category-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.category-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
}

.category-description {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.category-stats {
    display: flex;
    gap: 2rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #667eea;
    font-weight: 500;
}

.stat-item i {
    font-size: 1.2rem;
}

/* ===== FILTERS SECTION ===== */
.filters-section {
    padding: 2rem 0;
    background: white;
    border-bottom: 1px solid #eee;
    position: sticky;
    top: 80px;
    z-index: 100;
}

.search-box {
    position: relative;
    max-width: 400px;
}

.search-box input {
    padding-right: 3rem !important;
    border-radius: 25px !important;
    border: 2px solid #eee !important;
    transition: border-color 0.3s ease;
}

.search-box input:focus {
    outline: none !important;
    border-color: #667eea !important;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
}

.search-box i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    z-index: 10;
}

.filter-btn {
    padding: 0.5rem 1rem !important;
    border: 2px solid #eee !important;
    background: white !important;
    border-radius: 20px !important;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    color: #333 !important;
}

.filter-btn:hover,
.filter-btn.active {
    background: #667eea !important;
    color: white !important;
    border-color: #667eea !important;
}

.sort-options select {
    border-radius: 10px !important;
    border: 2px solid #eee !important;
    font-size: 0.9rem;
}

/* ===== ITEMS SECTION ===== */
.items-section {
    padding: 2rem 0 4rem;
    background: #f8f9fa;
}

.item-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.item-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.item-card-image {
    height: 200px;
    background-size: cover;
    background-position: center;
    position: relative;
}

.item-card-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: #ff6b6b;
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.item-card-favorite {
    position: absolute;
    top: 1rem;
    left: 1rem;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.item-card-favorite:hover {
    background: white;
    transform: scale(1.1);
}

.item-card-favorite.active {
    color: #ff6b6b;
}

.item-card-content {
    padding: 1.5rem;
}

.item-card-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.item-card-description {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.item-card-features {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.feature-tag {
    padding: 0.2rem 0.6rem;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: 500;
}

.feature-tag.spicy {
    background: #ffe6e6;
    color: #ff4757;
}

.feature-tag.vegetarian {
    background: #e6ffe6;
    color: #2ed573;
}

.feature-tag.popular {
    background: #fff3e0;
    color: #ff9500;
}

.item-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.item-card-price {
    font-size: 1.3rem;
    font-weight: 700;
    color: #667eea;
}

.item-card-rating {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    color: #ffa500;
    font-size: 0.9rem;
}

/* ===== NO RESULTS ===== */
.no-results {
    text-align: center;
    padding: 4rem 2rem;
    color: #666;
}

.no-results i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #ddd;
}

.no-results h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

/* ===== ITEM DETAILS PAGE ===== */
.item-details {
    padding: 2rem 0;
}

.item-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.item-gallery {
    position: sticky;
    top: 100px;
}

.item-gallery-main {
    border-radius: 20px;
    overflow: hidden;
    margin-bottom: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.item-gallery-main .swiper-slide {
    height: 400px;
}

.item-gallery-main .swiper-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.item-gallery-thumbs {
    height: 80px;
}

.item-gallery-thumbs .swiper-slide {
    height: 80px;
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    opacity: 0.5;
    transition: opacity 0.3s ease;
}

.item-gallery-thumbs .swiper-slide-thumb-active {
    opacity: 1;
}

.item-gallery-thumbs .swiper-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.item-info {
    padding: 1rem 0;
}

.item-header {
    margin-bottom: 1.5rem;
}

.item-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
}

.item-rating {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stars {
    display: flex;
    gap: 0.2rem;
    color: #ffa500;
}

.rating-text {
    color: #666;
    font-size: 0.9rem;
}

.item-price {
    margin-bottom: 2rem;
}

.current-price {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
}

.old-price {
    font-size: 1.2rem;
    color: #999;
    text-decoration: line-through;
    margin-right: 1rem;
}

.item-description,
.item-ingredients,
.item-nutrition {
    margin-bottom: 2rem;
}

.item-description h3,
.item-ingredients h3,
.item-nutrition h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.item-description p {
    color: #666;
    line-height: 1.6;
}

.item-ingredients ul {
    list-style: none;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.5rem;
}

.item-ingredients li {
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 10px;
    color: #666;
    font-size: 0.9rem;
}

.nutrition-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.nutrition-item {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 15px;
}

.nutrition-label {
    display: block;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.nutrition-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
}

.item-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 2rem;
}

.quantity-selector {
    display: flex;
    align-items: center;
    border: 2px solid #eee;
    border-radius: 10px;
    overflow: hidden;
}

.quantity-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: #f8f9fa;
    cursor: pointer;
    font-size: 1.2rem;
    font-weight: 600;
    transition: background 0.3s ease;
}

.quantity-btn:hover {
    background: #667eea;
    color: white;
}

.quantity-value {
    width: 60px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    background: white;
}

.add-to-cart-btn {
    flex: 1;
    padding: 1rem 2rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.add-to-cart-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.favorite-btn {
    width: 50px;
    height: 50px;
    border: 2px solid #eee;
    background: white;
    border-radius: 10px;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.favorite-btn:hover {
    border-color: #ff6b6b;
    color: #ff6b6b;
}

.favorite-btn.active {
    background: #ff6b6b;
    color: white;
    border-color: #ff6b6b;
}

/* ===== RELATED ITEMS ===== */
.related-items {
    background: #f8f9fa;
}

.related-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

/* ===== REVIEWS SECTION ===== */
.reviews-section {
    background: white;
}

.reviews-summary {
    margin-bottom: 3rem;
}

.overall-rating {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 20px;
    max-width: 400px;
    margin: 0 auto;
}

.rating-number {
    font-size: 3rem;
    font-weight: 700;
    color: #667eea;
}

.rating-stars {
    display: flex;
    gap: 0.2rem;
    color: #ffa500;
    font-size: 1.5rem;
}

.reviews-count {
    color: #666;
    font-size: 0.9rem;
}

.reviews-list {
    display: grid;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.review-card {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #f0f0f0;
}

.review-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.review-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.2rem;
}

.review-info {
    flex: 1;
}

.review-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.2rem;
}

.review-date {
    font-size: 0.8rem;
    color: #999;
}

.review-rating {
    display: flex;
    gap: 0.2rem;
    color: #ffa500;
}

.review-text {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.review-helpful {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #999;
}

.review-helpful button {
    background: none;
    border: none;
    color: #667eea;
    cursor: pointer;
    font-size: 0.9rem;
    padding: 0.2rem 0.5rem;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.review-helpful button:hover {
    background: #f0f0f0;
}

.load-more-reviews {
    display: block;
    margin: 0 auto;
    padding: 1rem 2rem;
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.load-more-reviews:hover {
    background: #667eea;
    color: white;
}

/* ===== RESPONSIVE DESIGN ADDITIONS ===== */
@media (max-width: 1024px) {
    .item-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .item-gallery {
        position: static;
    }

    .filters-section {
        position: static;
    }
}

@media (max-width: 768px) {
    .category-header {
        padding: 100px 0 1.5rem;
    }

    .category-title {
        font-size: 2rem;
    }

    .item-title {
        font-size: 2rem;
    }

    .item-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .add-to-cart-btn {
        width: 100%;
    }

    .overall-rating {
        flex-direction: column;
        gap: 0.5rem;
    }

    .rating-number {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .category-header {
        padding: 90px 0 1rem;
    }

    .category-title {
        font-size: 1.8rem;
    }

    .item-card-content {
        padding: 1rem;
    }

    .item-card-title {
        font-size: 1.1rem;
    }

    .item-card-description {
        font-size: 0.85rem;
    }

    .nutrition-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .item-ingredients ul {
        grid-template-columns: 1fr;
    }

    .review-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .review-avatar {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .filter-options {
        justify-content: center;
    }

    .filter-btn {
        font-size: 0.8rem !important;
        padding: 0.4rem 0.8rem !important;
    }
}
