// ===== MOCK API DATA =====
const API_DATA = {
    categories: [
        {
            id: 1,
            name: "الأطباق الرئيسية",
            description: "مجموعة متنوعة من الأطباق الرئيسية الشهية",
            image: "https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=400&h=300&fit=crop",
            itemsCount: 12
        },
        {
            id: 2,
            name: "المقبلات",
            description: "مقبلات شهية لبداية مثالية للوجبة",
            image: "https://images.unsplash.com/photo-1541014741259-de529411b96a?w=400&h=300&fit=crop",
            itemsCount: 8
        },
        {
            id: 3,
            name: "الحلويات",
            description: "حلويات لذيذة لإنهاء الوجبة بطعم رائع",
            image: "https://images.unsplash.com/photo-1551024506-0bccd828d307?w=400&h=300&fit=crop",
            itemsCount: 10
        },
        {
            id: 4,
            name: "المشروبات",
            description: "مشروبات منعشة وساخنة لجميع الأوقات",
            image: "https://images.unsplash.com/photo-1544145945-f90425340c7e?w=400&h=300&fit=crop",
            itemsCount: 15
        },
        {
            id: 5,
            name: "السلطات",
            description: "سلطات طازجة وصحية بمكونات طبيعية",
            image: "https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=300&fit=crop",
            itemsCount: 6
        },
        {
            id: 6,
            name: "المشاوي",
            description: "لحوم مشوية بطريقة احترافية ونكهات مميزة",
            image: "https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=400&h=300&fit=crop",
            itemsCount: 9
        }
    ],
    
    items: [
        // Main Dishes
        {
            id: 1,
            categoryId: 1,
            name: "كبسة الدجاج",
            description: "كبسة دجاج شهية محضرة بالطريقة التقليدية مع الأرز البسمتي والخضار الطازجة والتوابل الخاصة",
            price: 45,
            oldPrice: 55,
            rating: 4.5,
            reviewsCount: 125,
            images: [
                "https://images.unsplash.com/photo-1563379091339-03246963d96c?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=600&h=400&fit=crop"
            ],
            features: ["spicy", "popular"],
            ingredients: ["دجاج طازج", "أرز بسمتي", "خضار مشكلة", "توابل خاصة"],
            nutrition: {
                calories: 450,
                protein: 35,
                carbs: 45,
                fat: 15
            },
            isPopular: true,
            isSpicy: true
        },
        {
            id: 2,
            categoryId: 1,
            name: "مندي اللحم",
            description: "مندي لحم طري ولذيذ مطبوخ في الفرن التقليدي مع الأرز المدخن",
            price: 65,
            rating: 4.7,
            reviewsCount: 89,
            images: [
                "https://images.unsplash.com/photo-1574484284002-952d92456975?w=600&h=400&fit=crop"
            ],
            features: ["popular"],
            ingredients: ["لحم غنم طري", "أرز بسمتي", "توابل مندي", "خضار"],
            nutrition: {
                calories: 520,
                protein: 42,
                carbs: 38,
                fat: 22
            },
            isPopular: true
        },
        {
            id: 3,
            categoryId: 1,
            name: "برياني الروبيان",
            description: "برياني روبيان بالزعفران والتوابل الهندية الأصيلة",
            price: 55,
            rating: 4.3,
            reviewsCount: 67,
            images: [
                "https://images.unsplash.com/photo-1563379091339-03246963d96c?w=600&h=400&fit=crop"
            ],
            features: ["spicy"],
            ingredients: ["روبيان طازج", "أرز بسمتي", "زعفران", "توابل هندية"],
            nutrition: {
                calories: 380,
                protein: 28,
                carbs: 42,
                fat: 12
            },
            isSpicy: true
        },
        
        // Appetizers
        {
            id: 4,
            categoryId: 2,
            name: "حمص بالطحينة",
            description: "حمص كريمي مع الطحينة وزيت الزيتون والصنوبر",
            price: 18,
            rating: 4.2,
            reviewsCount: 45,
            images: [
                "https://images.unsplash.com/photo-1541014741259-de529411b96a?w=600&h=400&fit=crop"
            ],
            features: ["vegetarian"],
            ingredients: ["حمص", "طحينة", "زيت زيتون", "صنوبر"],
            nutrition: {
                calories: 180,
                protein: 8,
                carbs: 15,
                fat: 12
            },
            isVegetarian: true
        },
        {
            id: 5,
            categoryId: 2,
            name: "فتوش لبناني",
            description: "سلطة فتوش طازجة مع الخضار المشكلة والخبز المحمص",
            price: 22,
            rating: 4.4,
            reviewsCount: 38,
            images: [
                "https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=600&h=400&fit=crop"
            ],
            features: ["vegetarian"],
            ingredients: ["خس", "طماطم", "خيار", "خبز محمص", "سماق"],
            nutrition: {
                calories: 120,
                protein: 4,
                carbs: 18,
                fat: 5
            },
            isVegetarian: true
        },
        
        // Desserts
        {
            id: 6,
            categoryId: 3,
            name: "كنافة نابلسية",
            description: "كنافة نابلسية أصيلة بالجبن والقطر والفستق الحلبي",
            price: 25,
            rating: 4.6,
            reviewsCount: 92,
            images: [
                "https://images.unsplash.com/photo-1551024506-0bccd828d307?w=600&h=400&fit=crop"
            ],
            features: ["popular"],
            ingredients: ["كنافة", "جبن عكاوي", "قطر", "فستق حلبي"],
            nutrition: {
                calories: 320,
                protein: 12,
                carbs: 45,
                fat: 18
            },
            isPopular: true
        },
        
        // Beverages
        {
            id: 7,
            categoryId: 4,
            name: "عصير برتقال طازج",
            description: "عصير برتقال طبيعي 100% بدون إضافات",
            price: 12,
            rating: 4.1,
            reviewsCount: 28,
            images: [
                "https://images.unsplash.com/photo-1544145945-f90425340c7e?w=600&h=400&fit=crop"
            ],
            features: ["vegetarian"],
            ingredients: ["برتقال طازج"],
            nutrition: {
                calories: 85,
                protein: 2,
                carbs: 20,
                fat: 0
            },
            isVegetarian: true
        },
        
        // Salads
        {
            id: 8,
            categoryId: 5,
            name: "سلطة يونانية",
            description: "سلطة يونانية كلاسيكية مع الجبن الأبيض والزيتون",
            price: 28,
            rating: 4.3,
            reviewsCount: 34,
            images: [
                "https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=600&h=400&fit=crop"
            ],
            features: ["vegetarian"],
            ingredients: ["طماطم", "خيار", "جبن فيتا", "زيتون", "بصل أحمر"],
            nutrition: {
                calories: 150,
                protein: 8,
                carbs: 12,
                fat: 10
            },
            isVegetarian: true
        },
        
        // Grills
        {
            id: 9,
            categoryId: 6,
            name: "مشكل مشاوي",
            description: "تشكيلة من اللحوم المشوية: كباب، شيش طاووق، كفتة",
            price: 75,
            rating: 4.8,
            reviewsCount: 156,
            images: [
                "https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=600&h=400&fit=crop"
            ],
            features: ["popular"],
            ingredients: ["لحم غنم", "دجاج", "كفتة", "خضار مشوية"],
            nutrition: {
                calories: 580,
                protein: 48,
                carbs: 8,
                fat: 35
            },
            isPopular: true
        }
    ],
    
    reviews: [
        {
            id: 1,
            itemId: 1,
            userName: "أحمد محمد",
            rating: 5,
            comment: "كبسة رائعة جداً، الطعم أصيل والكمية مناسبة. أنصح بها بشدة!",
            date: "2024-01-15",
            helpful: 12
        },
        {
            id: 2,
            itemId: 1,
            userName: "فاطمة علي",
            rating: 4,
            comment: "طعم جميل لكن كان يمكن أن يكون أكثر حرارة قليلاً",
            date: "2024-01-10",
            helpful: 8
        },
        {
            id: 3,
            itemId: 2,
            userName: "خالد السعد",
            rating: 5,
            comment: "أفضل مندي جربته في المدينة! اللحم طري جداً والأرز مدخن بطريقة مثالية",
            date: "2024-01-12",
            helpful: 15
        }
    ]
};

// ===== API FUNCTIONS =====
class RestaurantAPI {
    constructor() {
        this.data = API_DATA;
    }
    
    // Get all categories
    async getCategories() {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve(this.data.categories);
            }, 500);
        });
    }
    
    // Get category by ID
    async getCategory(categoryId) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const category = this.data.categories.find(cat => cat.id === parseInt(categoryId));
                resolve(category);
            }, 300);
        });
    }
    
    // Get items by category
    async getItemsByCategory(categoryId, filters = {}) {
        return new Promise((resolve) => {
            setTimeout(() => {
                let items = this.data.items.filter(item => item.categoryId === parseInt(categoryId));
                
                // Apply filters
                if (filters.search) {
                    items = items.filter(item => 
                        item.name.includes(filters.search) || 
                        item.description.includes(filters.search)
                    );
                }
                
                if (filters.filter && filters.filter !== 'all') {
                    switch (filters.filter) {
                        case 'spicy':
                            items = items.filter(item => item.isSpicy);
                            break;
                        case 'vegetarian':
                            items = items.filter(item => item.isVegetarian);
                            break;
                        case 'popular':
                            items = items.filter(item => item.isPopular);
                            break;
                    }
                }
                
                // Apply sorting
                if (filters.sort) {
                    switch (filters.sort) {
                        case 'name':
                            items.sort((a, b) => a.name.localeCompare(b.name));
                            break;
                        case 'price-low':
                            items.sort((a, b) => a.price - b.price);
                            break;
                        case 'price-high':
                            items.sort((a, b) => b.price - a.price);
                            break;
                        case 'popular':
                            items.sort((a, b) => b.rating - a.rating);
                            break;
                    }
                }
                
                resolve(items);
            }, 400);
        });
    }
    
    // Get item by ID
    async getItem(itemId) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const item = this.data.items.find(item => item.id === parseInt(itemId));
                resolve(item);
            }, 300);
        });
    }
    
    // Get related items
    async getRelatedItems(itemId, limit = 4) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const item = this.data.items.find(item => item.id === parseInt(itemId));
                if (!item) {
                    resolve([]);
                    return;
                }
                
                const relatedItems = this.data.items
                    .filter(i => i.categoryId === item.categoryId && i.id !== item.id)
                    .slice(0, limit);
                    
                resolve(relatedItems);
            }, 300);
        });
    }
    
    // Get reviews for item
    async getItemReviews(itemId, limit = 5) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const reviews = this.data.reviews
                    .filter(review => review.itemId === parseInt(itemId))
                    .slice(0, limit);
                resolve(reviews);
            }, 300);
        });
    }
}

// Create global API instance
const api = new RestaurantAPI();
