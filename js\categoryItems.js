// ===== CATEGORY ITEMS PAGE CONTROLLER =====
class CategoryItemsPage {
    constructor() {
        this.categoryId = this.getCategoryIdFromURL();
        this.currentItems = [];
        this.filteredItems = [];
        this.currentFilters = {
            search: '',
            filter: 'all',
            sort: 'name'
        };
        this.favorites = JSON.parse(localStorage.getItem('favorites')) || [];
        
        this.init();
    }
    
    // Initialize the page
    async init() {
        this.showLoadingScreen();
        await this.loadCategoryData();
        await this.loadCategoryItems();
        this.setupEventListeners();
        this.hideLoadingScreen();
    }
    
    // Get category ID from URL parameters
    getCategoryIdFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        return parseInt(urlParams.get('category')) || 1;
    }
    
    // Show loading screen
    showLoadingScreen() {
        animationController.showLoadingScreen();
    }
    
    // Hide loading screen
    hideLoadingScreen() {
        setTimeout(() => {
            animationController.hideLoadingScreen();
        }, 1000);
    }
    
    // Load category data
    async loadCategoryData() {
        try {
            const category = await api.getCategory(this.categoryId);
            if (category) {
                this.renderCategoryHeader(category);
                this.updateBreadcrumb(category);
            }
        } catch (error) {
            console.error('Error loading category data:', error);
            this.showError('حدث خطأ في تحميل بيانات الصنف');
        }
    }
    
    // Load category items
    async loadCategoryItems() {
        try {
            const items = await api.getItemsByCategory(this.categoryId, this.currentFilters);
            this.currentItems = items;
            this.filteredItems = items;
            this.renderItems(items);
        } catch (error) {
            console.error('Error loading category items:', error);
            this.showError('حدث خطأ في تحميل المنتجات');
        }
    }
    
    // Setup event listeners
    setupEventListeners() {
        // Search input
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.currentFilters.search = e.target.value;
                this.applyFilters();
            });
        }
        
        // Filter buttons
        const filterButtons = document.querySelectorAll('.filter-btn');
        filterButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                // Remove active class from all buttons
                filterButtons.forEach(b => b.classList.remove('active'));
                // Add active class to clicked button
                e.target.classList.add('active');
                
                this.currentFilters.filter = e.target.getAttribute('data-filter');
                this.applyFilters();
            });
        });
        
        // Sort select
        const sortSelect = document.getElementById('sortSelect');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.currentFilters.sort = e.target.value;
                this.applyFilters();
            });
        }
        
        // Mobile menu toggle
        const navToggle = document.querySelector('.nav-toggle');
        const navMenu = document.querySelector('.nav-menu');
        
        if (navToggle && navMenu) {
            navToggle.addEventListener('click', () => {
                navMenu.classList.toggle('active');
            });
        }
    }
    
    // Apply filters and sorting
    async applyFilters() {
        this.showItemsLoading();
        
        try {
            const filteredItems = await api.getItemsByCategory(this.categoryId, this.currentFilters);
            this.filteredItems = filteredItems;
            this.renderItems(filteredItems);
        } catch (error) {
            console.error('Error applying filters:', error);
            this.showError('حدث خطأ في تطبيق الفلاتر');
        }
    }
    
    // Show items loading state
    showItemsLoading() {
        const itemsGrid = document.getElementById('itemsGrid');
        if (itemsGrid) {
            itemsGrid.innerHTML = `
                <div class="loading-items">
                    <div class="spinner"></div>
                    <p>جاري تحميل المنتجات...</p>
                </div>
            `;
        }
    }
    
    // Render category header
    renderCategoryHeader(category) {
        // Update category image
        const categoryImage = document.getElementById('categoryImage');
        if (categoryImage) {
            const img = categoryImage.querySelector('img');
            if (img) {
                img.src = category.image;
                img.alt = category.name;
            }
        }
        
        // Update category title
        const categoryTitle = document.getElementById('categoryTitle');
        if (categoryTitle) {
            categoryTitle.textContent = category.name;
        }
        
        // Update category description
        const categoryDescription = document.getElementById('categoryDescription');
        if (categoryDescription) {
            categoryDescription.textContent = category.description;
        }
        
        // Update items count
        const itemsCount = document.getElementById('itemsCount');
        if (itemsCount) {
            itemsCount.textContent = category.itemsCount;
        }
    }
    
    // Update breadcrumb
    updateBreadcrumb(category) {
        const categoryName = document.getElementById('categoryName');
        if (categoryName) {
            categoryName.textContent = category.name;
        }
        
        // Update page title
        document.title = `${category.name} - منيو المطعم الإلكتروني`;
    }
    
    // Render items
    renderItems(items) {
        const itemsGrid = document.getElementById('itemsGrid');
        const noResults = document.getElementById('noResults');
        
        if (!itemsGrid) return;
        
        if (items.length === 0) {
            itemsGrid.innerHTML = '';
            if (noResults) {
                noResults.style.display = 'block';
            }
            return;
        }
        
        if (noResults) {
            noResults.style.display = 'none';
        }
        
        itemsGrid.innerHTML = '';
        
        items.forEach((item, index) => {
            const itemCard = this.createItemCard(item);
            itemsGrid.appendChild(itemCard);
            
            // Add stagger animation
            gsap.fromTo(itemCard, 
                { opacity: 0, y: 30 },
                { 
                    opacity: 1, 
                    y: 0, 
                    duration: 0.6,
                    delay: index * 0.05,
                    ease: "power2.out"
                }
            );
        });
    }
    
    // Create item card element
    createItemCard(item) {
        const card = document.createElement('div');
        card.className = 'item-card hover-lift';
        card.setAttribute('data-aos', 'fade-up');
        
        const isFavorite = this.favorites.includes(item.id);
        const featuresHTML = item.features ? item.features.map(feature => 
            `<div class="feature-tag ${feature}">${this.getFeatureText(feature)}</div>`
        ).join('') : '';
        
        card.innerHTML = `
            <div class="item-card-image" style="background-image: url('${item.images[0]}')">
                ${item.oldPrice ? '<div class="item-card-badge">خصم</div>' : ''}
                <button class="item-card-favorite ${isFavorite ? 'active' : ''}" onclick="categoryItemsPage.toggleFavorite(${item.id}, this)">
                    <i class="${isFavorite ? 'fas' : 'far'} fa-heart"></i>
                </button>
            </div>
            <div class="item-card-content">
                <h3 class="item-card-title">${item.name}</h3>
                <p class="item-card-description">${item.description}</p>
                <div class="item-card-features">
                    ${featuresHTML}
                </div>
                <div class="item-card-footer">
                    <div class="item-card-price">
                        ${item.price} ريال
                        ${item.oldPrice ? `<span class="old-price">${item.oldPrice} ريال</span>` : ''}
                    </div>
                    <div class="item-card-rating">
                        <i class="fas fa-star"></i>
                        <span>${item.rating}</span>
                    </div>
                </div>
            </div>
        `;
        
        // Add click event to navigate to item details
        card.addEventListener('click', (e) => {
            // Don't navigate if clicking on favorite button
            if (!e.target.closest('.item-card-favorite')) {
                this.navigateToItem(item.id);
            }
        });
        
        return card;
    }
    
    // Get feature text in Arabic
    getFeatureText(feature) {
        const featureTexts = {
            'spicy': 'حار',
            'vegetarian': 'نباتي',
            'popular': 'الأكثر طلباً'
        };
        return featureTexts[feature] || feature;
    }
    
    // Toggle favorite
    toggleFavorite(itemId, button) {
        const isFavorite = this.favorites.includes(itemId);
        const icon = button.querySelector('i');
        
        if (isFavorite) {
            // Remove from favorites
            this.favorites = this.favorites.filter(id => id !== itemId);
            button.classList.remove('active');
            icon.className = 'far fa-heart';
            this.showNotification('تم إزالة العنصر من المفضلة', 'info');
        } else {
            // Add to favorites
            this.favorites.push(itemId);
            button.classList.add('active');
            icon.className = 'fas fa-heart';
            this.showNotification('تم إضافة العنصر للمفضلة', 'success');
        }
        
        // Save to localStorage
        localStorage.setItem('favorites', JSON.stringify(this.favorites));
        
        // Animate button
        gsap.to(button, {
            scale: 1.2,
            duration: 0.1,
            yoyo: true,
            repeat: 1,
            ease: "power2.out"
        });
    }
    
    // Navigate to item details page
    navigateToItem(itemId) {
        animationController.animatePageTransition(() => {
            window.location.href = `item.html?item=${itemId}&category=${this.categoryId}`;
        });
    }
    
    // Show notification
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        notification.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Animate out and remove
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
    
    // Show error message
    showError(message) {
        this.showNotification(message, 'error');
    }
}

// Initialize the page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.categoryItemsPage = new CategoryItemsPage();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CategoryItemsPage;
}
