// ===== ANIMATION CONTROLLER =====
class AnimationController {
    constructor() {
        this.initializeAOS();
        this.initializeGSAP();
        this.setupScrollAnimations();
        this.setupHoverAnimations();
    }
    
    // Initialize AOS (Animate On Scroll)
    initializeAOS() {
        AOS.init({
            duration: 800,
            easing: 'ease-out-cubic',
            once: true,
            offset: 100,
            delay: 0
        });
    }
    
    // Initialize GSAP animations
    initializeGSAP() {
        // Register ScrollTrigger plugin if available
        if (typeof ScrollTrigger !== 'undefined') {
            gsap.registerPlugin(ScrollTrigger);
        }
        
        // Set default GSAP settings
        gsap.defaults({
            duration: 0.6,
            ease: "power2.out"
        });
    }
    
    // Setup scroll-based animations
    setupScrollAnimations() {
        // Parallax effect for hero background
        if (document.querySelector('.hero-background')) {
            gsap.to('.hero-background', {
                yPercent: -50,
                ease: "none",
                scrollTrigger: {
                    trigger: '.hero',
                    start: "top bottom",
                    end: "bottom top",
                    scrub: true
                }
            });
        }
        
        // Stagger animation for category cards
        this.setupStaggerAnimation('.category-card', 0.1);
        this.setupStaggerAnimation('.item-card', 0.05);
        this.setupStaggerAnimation('.review-card', 0.1);
    }
    
    // Setup stagger animations
    setupStaggerAnimation(selector, stagger = 0.1) {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
            gsap.fromTo(elements, 
                {
                    opacity: 0,
                    y: 30
                },
                {
                    opacity: 1,
                    y: 0,
                    duration: 0.6,
                    stagger: stagger,
                    scrollTrigger: {
                        trigger: elements[0].parentElement,
                        start: "top 80%",
                        toggleActions: "play none none reverse"
                    }
                }
            );
        }
    }
    
    // Setup hover animations
    setupHoverAnimations() {
        // Card hover effects
        this.setupCardHoverEffect('.category-card');
        this.setupCardHoverEffect('.item-card');
        
        // Button hover effects
        this.setupButtonHoverEffect('.hero-btn');
        this.setupButtonHoverEffect('.add-to-cart-btn');
        this.setupButtonHoverEffect('.filter-btn');
    }
    
    // Card hover effect
    setupCardHoverEffect(selector) {
        const cards = document.querySelectorAll(selector);
        cards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                gsap.to(card, {
                    y: -10,
                    scale: 1.02,
                    duration: 0.3,
                    ease: "power2.out"
                });
                
                const overlay = card.querySelector('.category-overlay');
                if (overlay) {
                    gsap.to(overlay, {
                        opacity: 1,
                        duration: 0.3
                    });
                }
            });
            
            card.addEventListener('mouseleave', () => {
                gsap.to(card, {
                    y: 0,
                    scale: 1,
                    duration: 0.3,
                    ease: "power2.out"
                });
                
                const overlay = card.querySelector('.category-overlay');
                if (overlay) {
                    gsap.to(overlay, {
                        opacity: 0,
                        duration: 0.3
                    });
                }
            });
        });
    }
    
    // Button hover effect
    setupButtonHoverEffect(selector) {
        const buttons = document.querySelectorAll(selector);
        buttons.forEach(button => {
            button.addEventListener('mouseenter', () => {
                gsap.to(button, {
                    scale: 1.05,
                    duration: 0.2,
                    ease: "power2.out"
                });
            });
            
            button.addEventListener('mouseleave', () => {
                gsap.to(button, {
                    scale: 1,
                    duration: 0.2,
                    ease: "power2.out"
                });
            });
        });
    }
    
    // Loading screen animation
    showLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            gsap.set(loadingScreen, { display: 'flex', opacity: 1 });
            
            // Animate chef hat
            gsap.to('.chef-hat', {
                rotation: 360,
                duration: 2,
                repeat: -1,
                ease: "none"
            });
            
            // Animate loading text
            gsap.to('.loading-text', {
                opacity: 0.5,
                duration: 1,
                repeat: -1,
                yoyo: true,
                ease: "power2.inOut"
            });
        }
    }
    
    // Hide loading screen
    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            gsap.to(loadingScreen, {
                opacity: 0,
                duration: 0.5,
                ease: "power2.out",
                onComplete: () => {
                    loadingScreen.style.display = 'none';
                }
            });
        }
    }
    
    // Animate page transition
    animatePageTransition(callback) {
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            z-index: 9999;
            transform: translateX(-100%);
        `;
        document.body.appendChild(overlay);
        
        gsap.to(overlay, {
            x: 0,
            duration: 0.5,
            ease: "power2.inOut",
            onComplete: () => {
                if (callback) callback();
                gsap.to(overlay, {
                    x: '100%',
                    duration: 0.5,
                    ease: "power2.inOut",
                    delay: 0.1,
                    onComplete: () => {
                        document.body.removeChild(overlay);
                    }
                });
            }
        });
    }
    
    // Animate counter numbers
    animateCounter(element, target, duration = 2) {
        const obj = { value: 0 };
        gsap.to(obj, {
            value: target,
            duration: duration,
            ease: "power2.out",
            onUpdate: () => {
                element.textContent = Math.round(obj.value);
            }
        });
    }
    
    // Animate progress bars
    animateProgressBar(element, percentage, duration = 1.5) {
        gsap.fromTo(element, 
            { width: '0%' },
            { 
                width: percentage + '%',
                duration: duration,
                ease: "power2.out"
            }
        );
    }
    
    // Animate text typing effect
    animateTyping(element, text, speed = 50) {
        element.textContent = '';
        let i = 0;
        
        const typeInterval = setInterval(() => {
            element.textContent += text.charAt(i);
            i++;
            
            if (i >= text.length) {
                clearInterval(typeInterval);
            }
        }, speed);
    }
    
    // Animate element entrance
    animateEntrance(element, type = 'fadeInUp') {
        const animations = {
            fadeInUp: {
                from: { opacity: 0, y: 30 },
                to: { opacity: 1, y: 0 }
            },
            fadeInDown: {
                from: { opacity: 0, y: -30 },
                to: { opacity: 1, y: 0 }
            },
            fadeInLeft: {
                from: { opacity: 0, x: -30 },
                to: { opacity: 1, x: 0 }
            },
            fadeInRight: {
                from: { opacity: 0, x: 30 },
                to: { opacity: 1, x: 0 }
            },
            zoomIn: {
                from: { opacity: 0, scale: 0.8 },
                to: { opacity: 1, scale: 1 }
            }
        };
        
        const animation = animations[type] || animations.fadeInUp;
        
        gsap.fromTo(element, animation.from, {
            ...animation.to,
            duration: 0.6,
            ease: "power2.out"
        });
    }
    
    // Animate element exit
    animateExit(element, type = 'fadeOutUp', callback) {
        const animations = {
            fadeOutUp: { opacity: 0, y: -30 },
            fadeOutDown: { opacity: 0, y: 30 },
            fadeOutLeft: { opacity: 0, x: -30 },
            fadeOutRight: { opacity: 0, x: 30 },
            zoomOut: { opacity: 0, scale: 0.8 }
        };
        
        const animation = animations[type] || animations.fadeOutUp;
        
        gsap.to(element, {
            ...animation,
            duration: 0.4,
            ease: "power2.in",
            onComplete: callback
        });
    }
    
    // Refresh animations (useful after dynamic content loading)
    refresh() {
        AOS.refresh();
        if (typeof ScrollTrigger !== 'undefined') {
            ScrollTrigger.refresh();
        }
        this.setupHoverAnimations();
    }
}

// Create global animation controller instance
const animationController = new AnimationController();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AnimationController;
}
