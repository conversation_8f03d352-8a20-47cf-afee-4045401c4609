// ===== ITEM DETAILS PAGE CONTROLLER =====
class ItemDetailsPage {
    constructor() {
        this.itemId = this.getItemIdFromURL();
        this.categoryId = this.getCategoryIdFromURL();
        this.currentItem = null;
        this.quantity = 1;
        this.favorites = JSON.parse(localStorage.getItem('favorites')) || [];
        this.cart = JSON.parse(localStorage.getItem('cart')) || [];
        this.gallerySwiper = null;
        this.thumbsSwiper = null;
        
        this.init();
    }
    
    // Initialize the page
    async init() {
        this.showLoadingScreen();
        await this.loadItemData();
        await this.loadRelatedItems();
        await this.loadItemReviews();
        this.setupEventListeners();
        this.initializeGallery();
        this.hideLoadingScreen();
    }
    
    // Get item ID from URL parameters
    getItemIdFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        return parseInt(urlParams.get('item')) || 1;
    }
    
    // Get category ID from URL parameters
    getCategoryIdFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        return parseInt(urlParams.get('category')) || 1;
    }
    
    // Show loading screen
    showLoadingScreen() {
        animationController.showLoadingScreen();
    }
    
    // Hide loading screen
    hideLoadingScreen() {
        setTimeout(() => {
            animationController.hideLoadingScreen();
        }, 1000);
    }
    
    // Load item data
    async loadItemData() {
        try {
            const item = await api.getItem(this.itemId);
            if (item) {
                this.currentItem = item;
                this.renderItemDetails(item);
                this.updateBreadcrumb(item);
            } else {
                this.showError('المنتج غير موجود');
            }
        } catch (error) {
            console.error('Error loading item data:', error);
            this.showError('حدث خطأ في تحميل بيانات المنتج');
        }
    }
    
    // Load related items
    async loadRelatedItems() {
        try {
            const relatedItems = await api.getRelatedItems(this.itemId);
            this.renderRelatedItems(relatedItems);
        } catch (error) {
            console.error('Error loading related items:', error);
        }
    }
    
    // Load item reviews
    async loadItemReviews() {
        try {
            const reviews = await api.getItemReviews(this.itemId);
            this.renderReviews(reviews);
        } catch (error) {
            console.error('Error loading reviews:', error);
        }
    }
    
    // Setup event listeners
    setupEventListeners() {
        // Mobile menu toggle
        const navToggle = document.querySelector('.nav-toggle');
        const navMenu = document.querySelector('.nav-menu');
        
        if (navToggle && navMenu) {
            navToggle.addEventListener('click', () => {
                navMenu.classList.toggle('active');
            });
        }
        
        // Load more reviews button
        const loadMoreBtn = document.getElementById('loadMoreReviews');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMoreReviews();
            });
        }
    }
    
    // Initialize image gallery
    initializeGallery() {
        // Initialize thumbnails swiper first
        this.thumbsSwiper = new Swiper('.item-gallery-thumbs', {
            spaceBetween: 10,
            slidesPerView: 4,
            freeMode: true,
            watchSlidesProgress: true,
        });
        
        // Initialize main gallery swiper
        this.gallerySwiper = new Swiper('.item-gallery-main', {
            spaceBetween: 10,
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            thumbs: {
                swiper: this.thumbsSwiper,
            },
        });
    }
    
    // Render item details
    renderItemDetails(item) {
        // Update page title
        document.title = `${item.name} - منيو المطعم الإلكتروني`;
        
        // Update item title
        const itemTitle = document.getElementById('itemTitle');
        if (itemTitle) {
            itemTitle.textContent = item.name;
        }
        
        // Update item price
        const itemPrice = document.getElementById('itemPrice');
        if (itemPrice) {
            itemPrice.textContent = `${item.price} ريال`;
        }
        
        // Update old price if exists
        const itemOldPrice = document.getElementById('itemOldPrice');
        if (itemOldPrice && item.oldPrice) {
            itemOldPrice.textContent = `${item.oldPrice} ريال`;
            itemOldPrice.style.display = 'inline';
        }
        
        // Update description
        const itemDescription = document.getElementById('itemDescription');
        if (itemDescription) {
            itemDescription.textContent = item.description;
        }
        
        // Update rating
        this.updateRating(item.rating, item.reviewsCount);
        
        // Update features
        this.updateFeatures(item.features);
        
        // Update ingredients
        this.updateIngredients(item.ingredients);
        
        // Update nutrition
        this.updateNutrition(item.nutrition);
        
        // Update gallery
        this.updateGallery(item.images);
    }
    
    // Update rating display
    updateRating(rating, reviewsCount) {
        const ratingStars = document.getElementById('itemRating');
        const ratingText = document.querySelector('.rating-text');
        
        if (ratingStars) {
            ratingStars.innerHTML = this.generateStarsHTML(rating);
        }
        
        if (ratingText) {
            ratingText.textContent = `${rating} (${reviewsCount} تقييم)`;
        }
    }
    
    // Generate stars HTML
    generateStarsHTML(rating) {
        let starsHTML = '';
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 !== 0;
        
        for (let i = 0; i < fullStars; i++) {
            starsHTML += '<i class="fas fa-star"></i>';
        }
        
        if (hasHalfStar) {
            starsHTML += '<i class="fas fa-star-half-alt"></i>';
        }
        
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
        for (let i = 0; i < emptyStars; i++) {
            starsHTML += '<i class="far fa-star"></i>';
        }
        
        return starsHTML;
    }
    
    // Update features
    updateFeatures(features) {
        const featuresContainer = document.getElementById('itemFeatures');
        if (featuresContainer && features) {
            featuresContainer.innerHTML = features.map(feature => 
                `<div class="feature-tag ${feature}">${this.getFeatureText(feature)}</div>`
            ).join('');
        }
    }
    
    // Get feature text in Arabic
    getFeatureText(feature) {
        const featureTexts = {
            'spicy': 'حار',
            'vegetarian': 'نباتي',
            'popular': 'الأكثر طلباً'
        };
        return featureTexts[feature] || feature;
    }
    
    // Update ingredients
    updateIngredients(ingredients) {
        const ingredientsList = document.getElementById('itemIngredients');
        if (ingredientsList && ingredients) {
            ingredientsList.innerHTML = ingredients.map(ingredient => 
                `<li>${ingredient}</li>`
            ).join('');
        }
    }
    
    // Update nutrition information
    updateNutrition(nutrition) {
        const nutritionGrid = document.getElementById('itemNutrition');
        if (nutritionGrid && nutrition) {
            nutritionGrid.innerHTML = `
                <div class="nutrition-item">
                    <span class="nutrition-label">السعرات الحرارية</span>
                    <span class="nutrition-value">${nutrition.calories} كالوري</span>
                </div>
                <div class="nutrition-item">
                    <span class="nutrition-label">البروتين</span>
                    <span class="nutrition-value">${nutrition.protein} جرام</span>
                </div>
                <div class="nutrition-item">
                    <span class="nutrition-label">الكربوهيدرات</span>
                    <span class="nutrition-value">${nutrition.carbs} جرام</span>
                </div>
                <div class="nutrition-item">
                    <span class="nutrition-label">الدهون</span>
                    <span class="nutrition-value">${nutrition.fat} جرام</span>
                </div>
            `;
        }
    }
    
    // Update gallery images
    updateGallery(images) {
        const mainGallery = document.getElementById('mainGallery');
        const thumbsGallery = document.getElementById('thumbsGallery');
        
        if (mainGallery && images) {
            mainGallery.innerHTML = images.map(image => 
                `<div class="swiper-slide">
                    <img src="${image}" alt="${this.currentItem.name}">
                </div>`
            ).join('');
        }
        
        if (thumbsGallery && images) {
            thumbsGallery.innerHTML = images.map(image => 
                `<div class="swiper-slide">
                    <img src="${image}" alt="${this.currentItem.name}">
                </div>`
            ).join('');
        }
    }
    
    // Update breadcrumb
    updateBreadcrumb(item) {
        const itemBreadcrumb = document.getElementById('itemBreadcrumb');
        if (itemBreadcrumb) {
            itemBreadcrumb.textContent = item.name;
        }
        
        // Update category breadcrumb link
        const categoryBreadcrumb = document.getElementById('categoryBreadcrumb');
        if (categoryBreadcrumb) {
            categoryBreadcrumb.href = `categoryItems.html?category=${this.categoryId}`;
        }
    }
    
    // Render related items
    renderRelatedItems(items) {
        const relatedItemsGrid = document.getElementById('relatedItemsGrid');
        if (!relatedItemsGrid || !items.length) return;
        
        relatedItemsGrid.innerHTML = items.map(item => `
            <div class="item-card hover-lift" onclick="itemDetailsPage.navigateToItem(${item.id})">
                <div class="item-card-image" style="background-image: url('${item.images[0]}')"></div>
                <div class="item-card-content">
                    <h3 class="item-card-title">${item.name}</h3>
                    <div class="item-card-footer">
                        <div class="item-card-price">${item.price} ريال</div>
                        <div class="item-card-rating">
                            <i class="fas fa-star"></i>
                            <span>${item.rating}</span>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    // Render reviews
    renderReviews(reviews) {
        const reviewsList = document.getElementById('reviewsList');
        if (!reviewsList || !reviews.length) return;
        
        reviewsList.innerHTML = reviews.map(review => `
            <div class="review-card">
                <div class="review-header">
                    <div class="review-avatar">${review.userName.charAt(0)}</div>
                    <div class="review-info">
                        <div class="review-name">${review.userName}</div>
                        <div class="review-date">${this.formatDate(review.date)}</div>
                    </div>
                    <div class="review-rating">
                        ${this.generateStarsHTML(review.rating)}
                    </div>
                </div>
                <div class="review-text">${review.comment}</div>
                <div class="review-helpful">
                    <span>هل كان هذا التقييم مفيداً؟</span>
                    <button onclick="itemDetailsPage.markHelpful(${review.id})">
                        نعم (${review.helpful})
                    </button>
                </div>
            </div>
        `).join('');
    }
    
    // Format date
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    }
    
    // Change quantity
    changeQuantity(change) {
        this.quantity = Math.max(1, this.quantity + change);
        const quantityElement = document.getElementById('quantity');
        if (quantityElement) {
            quantityElement.textContent = this.quantity;
        }
    }
    
    // Add to cart
    addToCart() {
        if (!this.currentItem) return;
        
        const existingItem = this.cart.find(item => item.id === this.itemId);
        
        if (existingItem) {
            existingItem.quantity += this.quantity;
        } else {
            this.cart.push({ 
                id: this.itemId, 
                quantity: this.quantity,
                name: this.currentItem.name,
                price: this.currentItem.price,
                image: this.currentItem.images[0]
            });
        }
        
        localStorage.setItem('cart', JSON.stringify(this.cart));
        this.showNotification(`تم إضافة ${this.quantity} من ${this.currentItem.name} للسلة`, 'success');
        
        // Animate add to cart button
        const addToCartBtn = document.querySelector('.add-to-cart-btn');
        if (addToCartBtn) {
            gsap.to(addToCartBtn, {
                scale: 1.1,
                duration: 0.1,
                yoyo: true,
                repeat: 1,
                ease: "power2.out"
            });
        }
    }
    
    // Toggle favorite
    toggleFavorite() {
        const favoriteBtn = document.querySelector('.favorite-btn');
        const icon = favoriteBtn.querySelector('i');
        const isFavorite = this.favorites.includes(this.itemId);
        
        if (isFavorite) {
            this.favorites = this.favorites.filter(id => id !== this.itemId);
            favoriteBtn.classList.remove('active');
            icon.className = 'far fa-heart';
            this.showNotification('تم إزالة العنصر من المفضلة', 'info');
        } else {
            this.favorites.push(this.itemId);
            favoriteBtn.classList.add('active');
            icon.className = 'fas fa-heart';
            this.showNotification('تم إضافة العنصر للمفضلة', 'success');
        }
        
        localStorage.setItem('favorites', JSON.stringify(this.favorites));
        
        // Animate button
        gsap.to(favoriteBtn, {
            scale: 1.2,
            duration: 0.1,
            yoyo: true,
            repeat: 1,
            ease: "power2.out"
        });
    }
    
    // Navigate to item
    navigateToItem(itemId) {
        animationController.animatePageTransition(() => {
            window.location.href = `item.html?item=${itemId}&category=${this.categoryId}`;
        });
    }
    
    // Show notification
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        notification.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
    
    // Show error
    showError(message) {
        this.showNotification(message, 'error');
    }
}

// Global functions for HTML onclick events
function changeQuantity(change) {
    if (window.itemDetailsPage) {
        window.itemDetailsPage.changeQuantity(change);
    }
}

function addToCart() {
    if (window.itemDetailsPage) {
        window.itemDetailsPage.addToCart();
    }
}

function toggleFavorite() {
    if (window.itemDetailsPage) {
        window.itemDetailsPage.toggleFavorite();
    }
}

// Initialize the page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.itemDetailsPage = new ItemDetailsPage();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ItemDetailsPage;
}
